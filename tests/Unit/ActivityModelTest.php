<?php

use App\Models\Activity;
use App\Models\ExportedActivity;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Activity Model', function () {
    
    beforeEach(function () {
        $this->user = User::factory()->create();
    });

    describe('Model Attributes and Casting', function () {
        
        it('casts route_points to array', function () {
            $routePoints = [
                [37.7749, -122.4194],
                [37.7750, -122.4195]
            ];
            
            $activity = Activity::factory()->create([
                'user_id' => $this->user->id,
                'route_points' => $routePoints
            ]);
            
            expect($activity->route_points)->toBeArray();
            expect($activity->route_points)->toEqual($routePoints);
        });

        it('casts activity_date to date', function () {
            $activity = Activity::factory()->create([
                'user_id' => $this->user->id,
                'activity_date' => '2024-01-15'
            ]);
            
            expect($activity->activity_date)->toBeInstanceOf(\Illuminate\Support\Carbon::class);
            expect($activity->activity_date->format('Y-m-d'))->toBe('2024-01-15');
        });

        it('casts duration to float', function () {
            $activity = Activity::factory()->create([
                'user_id' => $this->user->id,
                'duration' => '1800.5'
            ]);
            
            expect($activity->duration)->toBeFloat();
            expect($activity->duration)->toBe(1800.5);
        });

        it('casts pace to float', function () {
            $activity = Activity::factory()->create([
                'user_id' => $this->user->id,
                'pace' => '5.5'
            ]);
            
            expect($activity->pace)->toBeFloat();
            expect($activity->pace)->toBe(5.5);
        });

        it('includes formatted_pace in appends', function () {
            $activity = Activity::factory()->create([
                'user_id' => $this->user->id,
                'pace' => 5.5
            ]);
            
            $array = $activity->toArray();
            expect($array)->toHaveKey('formatted_pace');
            expect($array['formatted_pace'])->toBe('5:30 min/km');
        });
    });

    describe('Relationships', function () {
        
        it('belongs to user', function () {
            $activity = Activity::factory()->create(['user_id' => $this->user->id]);
            
            expect($activity->user)->toBeInstanceOf(User::class);
            expect($activity->user->id)->toBe($this->user->id);
        });

        it('has many exports', function () {
            $activity = Activity::factory()->create(['user_id' => $this->user->id]);
            
            ExportedActivity::factory()->count(3)->create([
                'user_id' => $this->user->id,
                'activity_id' => $activity->id
            ]);
            
            expect($activity->exports)->toHaveCount(3);
            expect($activity->exports->first())->toBeInstanceOf(ExportedActivity::class);
        });

        it('user has many activities', function () {
            Activity::factory()->count(5)->create(['user_id' => $this->user->id]);
            
            expect($this->user->activities)->toHaveCount(5);
            expect($this->user->activities->first())->toBeInstanceOf(Activity::class);
        });
    });

    describe('Pace Formatting', function () {
        
        it('formats pace correctly for whole minutes', function () {
            $formatted = Activity::formatPaceDisplay(5.0);
            expect($formatted)->toBe('5:00 min/km');
        });

        it('formats pace correctly with seconds', function () {
            $formatted = Activity::formatPaceDisplay(5.5);
            expect($formatted)->toBe('5:30 min/km');
        });

        it('formats pace correctly with partial seconds', function () {
            $formatted = Activity::formatPaceDisplay(5.75);
            expect($formatted)->toBe('5:45 min/km');
        });

        it('formats pace correctly for fast pace', function () {
            $formatted = Activity::formatPaceDisplay(3.25);
            expect($formatted)->toBe('3:15 min/km');
        });

        it('formats pace correctly for slow pace', function () {
            $formatted = Activity::formatPaceDisplay(8.83);
            expect($formatted)->toBe('8:49 min/km');
        });

        it('handles edge case of very fast pace', function () {
            $formatted = Activity::formatPaceDisplay(2.0);
            expect($formatted)->toBe('2:00 min/km');
        });

        it('handles edge case of very slow pace', function () {
            $formatted = Activity::formatPaceDisplay(15.0);
            expect($formatted)->toBe('15:00 min/km');
        });

        it('rounds seconds correctly', function () {
            $formatted = Activity::formatPaceDisplay(5.99);
            expect($formatted)->toBe('5:59 min/km');
        });

        it('handles decimal precision correctly', function () {
            $formatted = Activity::formatPaceDisplay(5.016666667); // 5:01
            expect($formatted)->toBe('5:01 min/km');
        });
    });

    describe('Formatted Pace Accessor', function () {
        
        it('returns formatted pace through accessor', function () {
            $activity = Activity::factory()->create([
                'user_id' => $this->user->id,
                'pace' => 5.5
            ]);
            
            expect($activity->formatted_pace)->toBe('5:30 min/km');
        });

        it('updates formatted pace when pace changes', function () {
            $activity = Activity::factory()->create([
                'user_id' => $this->user->id,
                'pace' => 5.0
            ]);
            
            expect($activity->formatted_pace)->toBe('5:00 min/km');
            
            $activity->pace = 6.25;
            expect($activity->formatted_pace)->toBe('6:15 min/km');
        });
    });

    describe('Mass Assignment', function () {
        
        it('allows mass assignment of fillable attributes', function () {
            $data = [
                'user_id' => $this->user->id,
                'name' => 'Test Run',
                'route_points' => [[37.7749, -122.4194]],
                'activity_date' => '2024-01-15',
                'activity_time' => '08:30:00',
                'duration' => 1800,
                'pace' => 5.5,
                'activity_type' => 'running',
                'gpx_data' => '<gpx></gpx>',
                'avg_heart_rate' => 150,
                'cadence' => 180,
                'distance' => 5000
            ];
            
            $activity = Activity::create($data);
            
            expect($activity->name)->toBe('Test Run');
            expect($activity->activity_type)->toBe('running');
            expect($activity->avg_heart_rate)->toBe(150);
            expect($activity->cadence)->toBe(180);
            expect($activity->distance)->toBe(5000);
        });

        it('protects non-fillable attributes', function () {
            $data = [
                'id' => 999,
                'user_id' => $this->user->id,
                'name' => 'Test Run',
                'route_points' => [[37.7749, -122.4194]],
                'activity_date' => '2024-01-15',
                'activity_time' => '08:30:00',
                'duration' => 1800,
                'pace' => 5.5,
                'created_at' => '2020-01-01 00:00:00',
                'updated_at' => '2020-01-01 00:00:00'
            ];
            
            $activity = Activity::create($data);
            
            expect($activity->id)->not->toBe(999);
            expect($activity->created_at->format('Y'))->toBe(date('Y'));
        });
    });

    describe('Factory States', function () {
        
        it('creates running activity with appropriate data', function () {
            $activity = Activity::factory()->running()->create(['user_id' => $this->user->id]);
            
            expect($activity->activity_type)->toBe('running');
            expect($activity->pace)->toBeBetween(4.0, 7.0);
            expect($activity->cadence)->toBeBetween(170, 190);
            expect($activity->avg_heart_rate)->toBeBetween(140, 180);
        });

        it('creates cycling activity with appropriate data', function () {
            $activity = Activity::factory()->cycling()->create(['user_id' => $this->user->id]);
            
            expect($activity->activity_type)->toBe('cycling');
            expect($activity->pace)->toBeBetween(2.0, 4.0);
            expect($activity->cadence)->toBeBetween(80, 100);
            expect($activity->avg_heart_rate)->toBeBetween(120, 160);
        });

        it('creates hiking activity with appropriate data', function () {
            $activity = Activity::factory()->hiking()->create(['user_id' => $this->user->id]);
            
            expect($activity->activity_type)->toBe('hiking');
            expect($activity->pace)->toBeBetween(8.0, 15.0);
            expect($activity->cadence)->toBeBetween(120, 150);
            expect($activity->avg_heart_rate)->toBeBetween(110, 150);
        });

        it('creates minimal activity with required data only', function () {
            $activity = Activity::factory()->minimal()->create(['user_id' => $this->user->id]);
            
            expect($activity->route_points)->toHaveCount(3);
            expect($activity->avg_heart_rate)->toBeNull();
            expect($activity->cadence)->toBeNull();
            expect($activity->distance)->toBeNull();
        });

        it('creates complex route activity with many points', function () {
            $activity = Activity::factory()->complexRoute()->create(['user_id' => $this->user->id]);
            
            expect($activity->route_points)->toHaveCount(100);
            expect($activity->duration)->toBe(3600);
            expect($activity->distance)->toBe(10000);
        });
    });

    describe('Data Validation', function () {
        
        it('stores route points as valid JSON', function () {
            $routePoints = [
                [37.7749, -122.4194],
                [37.7750, -122.4195],
                [37.7751, -122.4196]
            ];
            
            $activity = Activity::factory()->create([
                'user_id' => $this->user->id,
                'route_points' => $routePoints
            ]);
            
            // Verify it's stored as JSON in database
            $rawData = \DB::table('activities')->where('id', $activity->id)->first();
            $decodedPoints = json_decode($rawData->route_points, true);
            
            expect($decodedPoints)->toEqual($routePoints);
        });

        it('handles empty route points array', function () {
            $activity = Activity::factory()->create([
                'user_id' => $this->user->id,
                'route_points' => []
            ]);
            
            expect($activity->route_points)->toBeArray();
            expect($activity->route_points)->toHaveCount(0);
        });
    });
});
