<?php

use App\Models\Activity;
use App\Models\ExportedActivity;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = User::factory()->withManyTokens()->create();
    $this->activity = Activity::factory()->running()->create(['user_id' => $this->user->id]);

    $this->validGpxData = [
        'name' => 'Test Morning Run',
        'routePoints' => [
            [37.7749, -122.4194],
            [37.7750, -122.4195],
            [37.7751, -122.4196],
            [37.7752, -122.4197]
        ],
        'activityDate' => '2024-01-15',
        'activityTime' => '08:30:00',
        'duration' => 1800, // 30 minutes
        'pace' => 5.5, // 5:30 min/km
        'distance' => 5000, // 5km
        'includeHeartRate' => true,
        'heartRate' => 150,
        'includeElevation' => true,
        'includeCadence' => true,
        'cadence' => 180
    ];
});

// GPX Generation for Authenticated Users Tests

it('generates valid GPX file with correct structure', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/export/gpx', $this->validGpxData);

    $response->assertStatus(200)
        ->assertJsonStructure([
            'success',
            'filename',
            'content',
            'mimeType',
            'tokens_remaining',
            'pace_display'
        ]);

    $content = $response->json('content');

    // Verify GPX XML structure
    expect($content)->toContain('<?xml version="1.0"')
        ->toContain('<gpx')
        ->toContain('<metadata>')
        ->toContain('<trk>')
        ->toContain('<trkseg>')
        ->toContain('<trkpt')
        ->toContain('</gpx>');

    // Verify GPX is valid XML
    $xml = simplexml_load_string($content);
    expect($xml)->not->toBeFalse();

    // Verify track points are included
    expect($content)->toContain('lat="37.7749"')
        ->toContain('lon="-122.4194"');
});

it('includes correct metadata in GPX file', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/export/gpx', $this->validGpxData);
    $content = $response->json('content');

    expect($content)->toContain('Test Morning Run')
        ->toContain('Create My Run')
        ->toContain('https://create-my.run/')
        ->toContain('Activity exported from Garmin Connect using Create My Run');
});

it('includes heart rate data when requested', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/export/gpx', $this->validGpxData);
    $content = $response->json('content');

    expect($content)->toContain('<gpxtpx:hr>150</gpxtpx:hr>');
});

it('includes elevation data when requested', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/export/gpx', $this->validGpxData);
    $content = $response->json('content');

    expect($content)->toContain('<ele>');
});

it('includes cadence data when requested', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/export/gpx', $this->validGpxData);
    $content = $response->json('content');

    expect($content)->toContain('<gpxtpx:cad>180</gpxtpx:cad>');
});

it('generates correct filename', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/export/gpx', $this->validGpxData);

    expect($response->json('filename'))->toBe('Test_Morning_Run.gpx');
});

it('uses a token when generating GPX', function () {
    Sanctum::actingAs($this->user);
    $initialTokens = $this->user->tokens;

    $response = $this->postJson('/api/export/gpx', $this->validGpxData);

    $response->assertStatus(200);
    expect($response->json('tokens_remaining'))->toBe($initialTokens - 1);

    $this->user->refresh();
    expect($this->user->tokens)->toBe($initialTokens - 1);
});

it('creates activity record when activity_id not provided', function () {
    Sanctum::actingAs($this->user);

    $initialActivityCount = Activity::count();

    $response = $this->postJson('/api/export/gpx', $this->validGpxData);

    $response->assertStatus(200);
    expect(Activity::count())->toBe($initialActivityCount + 1);

    $newActivity = Activity::latest()->first();
    expect($newActivity->name)->toBe('Test Morning Run');
    expect($newActivity->user_id)->toBe($this->user->id);
});

it('creates exported activity record', function () {
    Sanctum::actingAs($this->user);

    $initialExportCount = ExportedActivity::count();

    $response = $this->postJson('/api/export/gpx', $this->validGpxData);

    $response->assertStatus(200);
    expect(ExportedActivity::count())->toBe($initialExportCount + 1);

    $export = ExportedActivity::latest()->first();
    expect($export->user_id)->toBe($this->user->id);
    expect($export->format)->toBe('gpx');
    expect($export->file_name)->toBe('Test_Morning_Run.gpx');
    expect($export->export_data)->toContain('<?xml version="1.0"');
});

it('uses existing activity when activity_id provided', function () {
    Sanctum::actingAs($this->user);

    $data = array_merge($this->validGpxData, ['activity_id' => $this->activity->id]);
    $initialActivityCount = Activity::count();

    $response = $this->postJson('/api/export/gpx', $data);

    $response->assertStatus(200);
    expect(Activity::count())->toBe($initialActivityCount); // No new activity created
});

it('formats pace display correctly', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/export/gpx', $this->validGpxData);

    expect($response->json('pace_display'))->toBe('5:30 min/km');
});

// Token Management Tests

it('rejects request when user has no tokens', function () {
    $userWithoutTokens = User::factory()->withoutTokens()->create();
    Sanctum::actingAs($userWithoutTokens);

    $response = $this->postJson('/api/export/gpx', $this->validGpxData);

    $response->assertStatus(403)
        ->assertJson([
            'message' => 'You do not have enough tokens to export this activity.',
            'tokens_available' => 0
        ]);
});

// Unauthenticated Access Tests

it('allows GPX generation without authentication', function () {
    $response = $this->postJson('/api/export/gpx', $this->validGpxData);

    $response->assertStatus(200)
        ->assertJsonStructure([
            'success',
            'filename',
            'content',
            'mimeType',
            'pace_display'
        ])
        ->assertJsonMissing(['tokens_remaining']);
});

it('does not create activity or export records for unauthenticated users', function () {
    $initialActivityCount = Activity::count();
    $initialExportCount = ExportedActivity::count();

    $response = $this->postJson('/api/export/gpx', $this->validGpxData);

    $response->assertStatus(200);
    expect(Activity::count())->toBe($initialActivityCount);
    expect(ExportedActivity::count())->toBe($initialExportCount);
});

// Validation and Error Handling Tests

it('validates required fields', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/export/gpx', []);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['name', 'routePoints', 'activityDate', 'activityTime', 'duration', 'pace']);
});

it('validates route points format', function () {
    Sanctum::actingAs($this->user);

    $invalidData = array_merge($this->validGpxData, ['routePoints' => 'invalid']);

    $response = $this->postJson('/api/export/gpx', $invalidData);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['routePoints']);
});

it('validates numeric fields', function () {
    Sanctum::actingAs($this->user);

    $invalidData = array_merge($this->validGpxData, [
        'duration' => 'invalid',
        'pace' => 'invalid',
        'heartRate' => 'invalid',
        'cadence' => 'invalid'
    ]);

    $response = $this->postJson('/api/export/gpx', $invalidData);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['duration', 'pace', 'heartRate', 'cadence']);
});

it('handles empty route points array', function () {
    Sanctum::actingAs($this->user);

    $invalidData = array_merge($this->validGpxData, ['routePoints' => []]);

    $response = $this->postJson('/api/export/gpx', $invalidData);

    // Should still process but create minimal GPX
    $response->assertStatus(200);
    $content = $response->json('content');
    expect($content)->toContain('<gpx');
});

it('handles extreme pace values', function () {
    Sanctum::actingAs($this->user);

    // Test very fast pace
    $fastPaceData = array_merge($this->validGpxData, ['pace' => 1.0]);
    $response = $this->postJson('/api/export/gpx', $fastPaceData);
    $response->assertStatus(200);

    // Test very slow pace
    $slowPaceData = array_merge($this->validGpxData, ['pace' => 20.0]);
    $response = $this->postJson('/api/export/gpx', $slowPaceData);
    $response->assertStatus(200);
});

it('handles invalid activity_id', function () {
    Sanctum::actingAs($this->user);

    $invalidData = array_merge($this->validGpxData, ['activity_id' => 99999]);

    $response = $this->postJson('/api/export/gpx', $invalidData);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['activity_id']);
});

// Activity Type Handling Tests

it('generates appropriate GPX for running activities', function () {
    Sanctum::actingAs($this->user);

    $runningData = array_merge($this->validGpxData, ['pace' => 5.0]);

    $response = $this->postJson('/api/export/gpx', $runningData);
    $content = $response->json('content');

    expect($content)->toContain('<type>running</type>');
});

it('handles different activity types in created activities', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/export/gpx', $this->validGpxData);

    $response->assertStatus(200);

    $newActivity = Activity::latest()->first();
    expect($newActivity->activity_type)->toBe('run'); // Default type set by controller
});

// GPX Content Validation Tests

it('generates GPX with proper timestamps', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/export/gpx', $this->validGpxData);
    $content = $response->json('content');

    // Check for ISO 8601 timestamp format
    expect($content)->toMatch('/<time>\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z<\/time>/');
});

it('generates GPX with speed extensions', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/export/gpx', $this->validGpxData);
    $content = $response->json('content');

    expect($content)->toContain('<gpxtpx:speed>');
});

it('includes proper GPX namespaces', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/export/gpx', $this->validGpxData);
    $content = $response->json('content');

    expect($content)->toContain('xmlns="http://www.topografix.com/GPX/1/1"')
        ->toContain('xmlns:gpxtpx="http://www.garmin.com/xmlschemas/TrackPointExtension/v2"');
});
