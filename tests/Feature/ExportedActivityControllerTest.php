<?php

use App\Models\Activity;
use App\Models\ExportedActivity;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = User::factory()->withManyTokens()->create();
    $this->otherUser = User::factory()->create();
    $this->activity = Activity::factory()->create(['user_id' => $this->user->id]);
    $this->otherActivity = Activity::factory()->create(['user_id' => $this->otherUser->id]);
});

// Index - Get All Exported Activities Tests

it('returns user exported activities with activity relationship', function () {
    Sanctum::actingAs($this->user);

    $exports = ExportedActivity::factory()->count(3)->create([
        'user_id' => $this->user->id,
        'activity_id' => $this->activity->id
    ]);

    // Create exports for other user
    ExportedActivity::factory()->count(2)->create([
        'user_id' => $this->otherUser->id,
        'activity_id' => $this->otherActivity->id
    ]);

    $response = $this->getJson('/api/exported-activities');

    $response->assertStatus(200)
        ->assertJsonCount(3)
        ->assertJsonStructure([
            '*' => [
                'id',
                'user_id',
                'activity_id',
                'format',
                'file_name',
                'export_data',
                'created_at',
                'updated_at',
                'activity' => [
                    'id',
                    'name',
                    'activity_date',
                    'activity_time',
                    'duration',
                    'pace'
                ]
            ]
        ]);

    // Verify only user's exports are returned
    $returnedUserIds = collect($response->json())->pluck('user_id')->unique()->toArray();
    expect($returnedUserIds)->toEqual([$this->user->id]);
});

it('returns empty array when user has no exported activities', function () {
    Sanctum::actingAs($this->user);

    $response = $this->getJson('/api/exported-activities');

    $response->assertStatus(200)
        ->assertJsonCount(0);
});

it('orders exported activities by latest first', function () {
    Sanctum::actingAs($this->user);

    $firstExport = ExportedActivity::factory()->create([
        'user_id' => $this->user->id,
        'activity_id' => $this->activity->id,
        'created_at' => now()->subHours(2)
    ]);

    $secondExport = ExportedActivity::factory()->create([
        'user_id' => $this->user->id,
        'activity_id' => $this->activity->id,
        'created_at' => now()->subHour()
    ]);

    $response = $this->getJson('/api/exported-activities');

    $response->assertStatus(200);

    $exportIds = collect($response->json())->pluck('id')->toArray();
    expect($exportIds)->toEqual([$secondExport->id, $firstExport->id]);
});

it('requires authentication for index', function () {
    $response = $this->getJson('/api/exported-activities');

    $response->assertStatus(401);
});

// Show - Get Single Exported Activity Tests

it('returns exported activity with activity relationship', function () {
    Sanctum::actingAs($this->user);

    $export = ExportedActivity::factory()->create([
        'user_id' => $this->user->id,
        'activity_id' => $this->activity->id
    ]);

    $response = $this->getJson("/api/exported-activities/{$export->id}");

    $response->assertStatus(200)
        ->assertJsonStructure([
            'id',
            'user_id',
            'activity_id',
            'format',
            'file_name',
            'export_data',
            'created_at',
            'updated_at',
            'activity' => [
                'id',
                'name',
                'activity_date',
                'activity_time',
                'duration',
                'pace'
            ]
        ]);

    expect($response->json('id'))->toBe($export->id);
    expect($response->json('activity.id'))->toBe($this->activity->id);
});

it('returns 404 for non-existent exported activity', function () {
    Sanctum::actingAs($this->user);

    $response = $this->getJson('/api/exported-activities/99999');

    $response->assertStatus(404);
});

it('returns 404 for other users exported activity', function () {
    Sanctum::actingAs($this->user);

    $otherExport = ExportedActivity::factory()->create([
        'user_id' => $this->otherUser->id,
        'activity_id' => $this->otherActivity->id
    ]);

    $response = $this->getJson("/api/exported-activities/{$otherExport->id}");

    $response->assertStatus(404);
});

it('requires authentication for show', function () {
    $export = ExportedActivity::factory()->create();

    $response = $this->getJson("/api/exported-activities/{$export->id}");

    $response->assertStatus(401);
});

// Download - Download Exported Activity File Tests

it('downloads GPX file with correct headers', function () {
    Sanctum::actingAs($this->user);

    $export = ExportedActivity::factory()->gpx()->create([
        'user_id' => $this->user->id,
        'activity_id' => $this->activity->id,
        'file_name' => 'test_run.gpx'
    ]);

    $response = $this->get("/api/exported-activities/{$export->id}/download");

    $response->assertStatus(200)
        ->assertHeader('Content-Type', 'application/gpx+xml')
        ->assertHeader('Content-Disposition', 'attachment; filename="test_run.gpx"');

    expect($response->getContent())->toContain('<?xml version="1.0"')
        ->toContain('<gpx');
});

it('downloads TCX file with correct headers', function () {
    Sanctum::actingAs($this->user);

    $export = ExportedActivity::factory()->tcx()->create([
        'user_id' => $this->user->id,
        'activity_id' => $this->activity->id,
        'file_name' => 'test_run.tcx'
    ]);

    $response = $this->get("/api/exported-activities/{$export->id}/download");

    $response->assertStatus(200)
        ->assertHeader('Content-Type', 'application/gpx+xml') // Note: Controller uses gpx+xml for all
        ->assertHeader('Content-Disposition', 'attachment; filename="test_run.tcx"');

    expect($response->getContent())->toContain('<?xml version="1.0"')
        ->toContain('<TrainingCenterDatabase');
});

it('downloads CSV file with correct headers', function () {
    Sanctum::actingAs($this->user);

    $export = ExportedActivity::factory()->csv()->create([
        'user_id' => $this->user->id,
        'activity_id' => $this->activity->id,
        'file_name' => 'test_run.csv'
    ]);

    $response = $this->get("/api/exported-activities/{$export->id}/download");

    $response->assertStatus(200)
        ->assertHeader('Content-Type', 'application/gpx+xml') // Note: Controller uses gpx+xml for all
        ->assertHeader('Content-Disposition', 'attachment; filename="test_run.csv"');

    expect($response->getContent())->toContain('timestamp,latitude,longitude');
});

it('returns 404 for non-existent exported activity on download', function () {
    Sanctum::actingAs($this->user);

    $response = $this->get('/api/exported-activities/99999/download');

    $response->assertStatus(404);
});

it('returns 404 for other users exported activity on download', function () {
    Sanctum::actingAs($this->user);

    $otherExport = ExportedActivity::factory()->create([
        'user_id' => $this->otherUser->id,
        'activity_id' => $this->otherActivity->id
    ]);

    $response = $this->get("/api/exported-activities/{$otherExport->id}/download");

    $response->assertStatus(404);
});

it('requires authentication for download', function () {
    $export = ExportedActivity::factory()->create();

    $response = $this->get("/api/exported-activities/{$export->id}/download");

    $response->assertStatus(401);
});

// Store - Log Exported Activity Tests

it('creates exported activity record with valid data', function () {
    Sanctum::actingAs($this->user);

    $data = [
        'activity_id' => $this->activity->id,
        'format' => 'gpx',
        'file_name' => 'custom_run.gpx',
        'export_data' => '<?xml version="1.0"?><gpx></gpx>'
    ];

    $response = $this->postJson('/api/exported-activities', $data);

    $response->assertStatus(201)
        ->assertJsonStructure([
            'message',
            'exported_activity' => [
                'id',
                'user_id',
                'activity_id',
                'format',
                'file_name',
                'export_data',
                'created_at',
                'updated_at'
            ]
        ]);

    $this->assertDatabaseHas('exported_activities', [
        'user_id' => $this->user->id,
        'activity_id' => $this->activity->id,
        'format' => 'gpx',
        'file_name' => 'custom_run.gpx'
    ]);
});

it('creates exported activity with default filename when not provided', function () {
    Sanctum::actingAs($this->user);

    $data = [
        'activity_id' => $this->activity->id,
        'format' => 'tcx'
    ];

    $response = $this->postJson('/api/exported-activities', $data);

    $response->assertStatus(201);

    $export = ExportedActivity::latest()->first();
    expect($export->file_name)->toBe('activity.tcx');
});

it('validates required fields', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/exported-activities', []);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['activity_id', 'format']);
});

it('validates activity_id exists', function () {
    Sanctum::actingAs($this->user);

    $data = [
        'activity_id' => 99999,
        'format' => 'gpx'
    ];

    $response = $this->postJson('/api/exported-activities', $data);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['activity_id']);
});

it('validates format is allowed value', function () {
    Sanctum::actingAs($this->user);

    $data = [
        'activity_id' => $this->activity->id,
        'format' => 'invalid_format'
    ];

    $response = $this->postJson('/api/exported-activities', $data);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['format']);
});

it('accepts valid formats', function () {
    Sanctum::actingAs($this->user);

    $validFormats = ['gpx', 'tcx', 'csv'];

    foreach ($validFormats as $format) {
        $data = [
            'activity_id' => $this->activity->id,
            'format' => $format
        ];

        $response = $this->postJson('/api/exported-activities', $data);
        $response->assertStatus(201);
    }
});

it('rejects request when user has no tokens', function () {
    $userWithoutTokens = User::factory()->withoutTokens()->create();
    Sanctum::actingAs($userWithoutTokens);

    $data = [
        'activity_id' => $this->activity->id,
        'format' => 'gpx'
    ];

    $response = $this->postJson('/api/exported-activities', $data);

    $response->assertStatus(403)
        ->assertJson([
            'message' => 'No tokens available for export'
        ]);
});

it('requires authentication for store', function () {
    $data = [
        'activity_id' => $this->activity->id,
        'format' => 'gpx'
    ];

    $response = $this->postJson('/api/exported-activities', $data);

    $response->assertStatus(401);
});

// Model Relationships Tests

it('exported activity belongs to user', function () {
    $export = ExportedActivity::factory()->create([
        'user_id' => $this->user->id,
        'activity_id' => $this->activity->id
    ]);

    expect($export->user)->toBeInstanceOf(User::class);
    expect($export->user->id)->toBe($this->user->id);
});

it('exported activity belongs to activity', function () {
    $export = ExportedActivity::factory()->create([
        'user_id' => $this->user->id,
        'activity_id' => $this->activity->id
    ]);

    expect($export->activity)->toBeInstanceOf(Activity::class);
    expect($export->activity->id)->toBe($this->activity->id);
});

it('user has many exported activities', function () {
    ExportedActivity::factory()->count(3)->create([
        'user_id' => $this->user->id,
        'activity_id' => $this->activity->id
    ]);

    expect($this->user->exportedActivities)->toHaveCount(3);
});

it('activity has many exports', function () {
    ExportedActivity::factory()->count(2)->create([
        'user_id' => $this->user->id,
        'activity_id' => $this->activity->id
    ]);

    expect($this->activity->exports)->toHaveCount(2);
});
